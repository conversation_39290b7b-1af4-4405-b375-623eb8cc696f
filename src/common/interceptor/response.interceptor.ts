import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

export interface BaseResponse<T> {
  code: number;
  message: string;
  data: T;
  timestamp: number;
}

@Injectable()
export class ResponseInterceptor<T>
  implements NestInterceptor<T, BaseResponse<T>>
{
  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<BaseResponse<T>> {
    console.log('interceptor');
    return next.handle().pipe(
      map((data) => ({
        code: 200,
        message: 'Success',
        data,
        timestamp: Date.now(),
      })),
    );
  }
}
