import { Request, Response, NextFunction } from 'express';
import { Logger } from '@nestjs/common';

export function loggerMiddleware(
  req: Request,
  res: Response,
  next: NextFunction,
) {
  const start = Date.now();
  const { method, url, body, query } = req;

  res.on('finish', () => {
    const { statusCode } = res;
    const responseTime = Date.now() - start;

    Logger.log(
      `${method} ${url} ${statusCode} ${responseTime}ms`,
      'HTTP',
      false, // 禁用日志前缀
    );

    // 记录详细信息（生产环境可选择性记录）
    if (statusCode >= 400) {
      Logger.error(
        `Request: ${JSON.stringify({ body, query })}\nResponse Time: ${responseTime}ms`,
        'HTTP-DETAIL',
        false,
      );
    }
  });

  next();
}
