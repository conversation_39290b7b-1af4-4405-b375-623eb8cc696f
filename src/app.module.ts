import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';
import { ConfigModule, ConfigService } from '@nestjs/config';

import { MetricsModule } from './common/metrics/metrics.module';
import { loggerMiddleware } from './common/middleware/request.middleware';
import { HttpExceptionFilter } from './common/filter/http-exception.filter';
import { ResponseInterceptor } from './common/interceptor/response.interceptor';
import { httpMetricsMiddleware } from './common/metrics/http-metrics.middleware';

import { UserModule } from './user/user.module';
import { ApplicationModule } from './application/application.module';
import { DeployEventModule } from './deploy-event/deploy-event.module';
import { AppController } from './app.controller';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get('DB_HOST'),
        port: configService.get('DB_PORT'),
        username: configService.get('DB_USERNAME'),
        password: configService.get('DB_PASSWORD'),
        database: configService.get('DB_DATABASE'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize: true, // 生产环境不建议开启
        logging: true, // 生产环境不建议开启
      }),
      inject: [ConfigService],
    }),
    MetricsModule,
    UserModule,
    ApplicationModule,
    DeployEventModule,
  ],
  controllers: [AppController],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseInterceptor,
    },
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    },
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer): any {
    consumer.apply(loggerMiddleware, httpMetricsMiddleware).forRoutes({
      path: '*',
      method: RequestMethod.ALL,
    });
  }
}
