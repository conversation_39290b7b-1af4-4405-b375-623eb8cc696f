import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('application')
export class Application {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'app_name', unique: true })
  appName: string;

  @Column({ name: 'app_code', unique: true })
  appCode: string;

  @Column({ name: 'app_desc', nullable: true })
  appDesc: string;

  @Column({ name: 'app_type' })
  appType: string;

  @Column({ name: 'owner_id' })
  ownerId: number;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
