import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
} from 'typeorm';

@Entity('deploy_event')
export class DeployEvent {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'app_id' })
  appId: number;

  @Column({ name: 'deploy_version' })
  deployVersion: string;

  @Column({ name: 'deploy_status' })
  deployStatus: string;

  @Column({ name: 'deploy_by' })
  deployBy: string;

  @Column({ name: 'deploy_at' })
  deployAt: Date;

  @Column({ name: 'deploy_log', type: 'text', nullable: true })
  deployLog: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;
}
