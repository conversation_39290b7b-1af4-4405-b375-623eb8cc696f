import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Injectable, NotFoundException } from '@nestjs/common';

import { DeployEvent } from './entities/deploy-event.entity';

@Injectable()
export class DeployEventService {
  constructor(
    @InjectRepository(DeployEvent)
    private deployEventRepository: Repository<DeployEvent>,
  ) {}

  // 创建数据
  async create(createDeployEventDto: any): Promise<DeployEvent> {
    // TODO 先更新应用信息, 新应用就接入, 老应用就更新字段信息

    // 再写入构建部署埋点表
    const deployEvent: any =
      this.deployEventRepository.create(createDeployEventDto);
    return this.deployEventRepository.save(deployEvent);
  }

  // 查询所有数据(一般不直接对外开放)
  async findAll(): Promise<Array<DeployEvent>> {
    return this.deployEventRepository.find();
  }

  // 根据 ID 查询数据详情
  async findOne(id: number): Promise<DeployEvent> {
    const deployEvent = await this.deployEventRepository.findOne({
      where: { id },
    });
    if (!deployEvent) {
      throw new NotFoundException(`DeployEvent with id ${id} not found`);
    }
    return deployEvent;
  }

  // 根据特定条件查询数据
  async findByOptions(queryOptions: any): Promise<Array<DeployEvent>> {
    const where = {};
    // 补全支持的查询场景
    if (queryOptions.ownerId) {
      where['ownerId'] = queryOptions.ownerId;
    }
    return this.deployEventRepository.find({
      where,
    });
  }

  // 更新数据信息
  async update(id: number, updateUserDto: any): Promise<DeployEvent> {
    await this.deployEventRepository.update(id, updateUserDto);
    return this.findOne(id);
  }

  // 删除用户
  async remove(id: number): Promise<void> {
    const user = await this.findOne(id);
    await this.deployEventRepository.remove(user);
  }

  // 新增或更新数据
  async save(user: DeployEvent): Promise<DeployEvent> {
    return this.deployEventRepository.save(user);
  }
}
