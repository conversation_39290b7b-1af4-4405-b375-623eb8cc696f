import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { DeployEventController } from './deploy-event.controller';
import { DeployEventService } from './deploy-event.service';
import { DeployEvent } from './entities/deploy-event.entity';

@Module({
  imports: [TypeOrmModule.forFeature([DeployEvent])],
  controllers: [DeployEventController],
  providers: [DeployEventService],
})
export class DeployEventModule {}
