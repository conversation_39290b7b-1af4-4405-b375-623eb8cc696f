import { Controller, Get, Post } from '@nestjs/common';

import { DeployEventService } from './deploy-event.service';
import { DeployEvent } from './entities/deploy-event.entity';

@Controller('/api/deploy-event')
export class DeployEventController {
  constructor(private readonly deployEventService: DeployEventService) {}

  @Post('report')
  async reportAppBuild(): Promise<boolean> {
    const resp = await this.deployEventService.findAll();
    console.log(resp);
    return true;
  }

  @Get('queryDeployRecord')
  async queryDeployRecord(): Promise<Array<DeployEvent>> {
    const resp = await this.deployEventService.findByOptions({});
    console.log(resp);
    return resp;
  }
}
