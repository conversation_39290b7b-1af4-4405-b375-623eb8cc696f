import * as process from 'node:process';
import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';

import { AppModule } from './app.module';

const PORT = process.env.PORT || 6001;

async function bootstrap() {
  const logger = new Logger('NestBootstrap');
  const app = await NestFactory.create(AppModule);
  await app.listen(PORT);
  console.log(process.pid);
  logger.log(`Nest App is running at http://127.0.0.1:${PORT}`);
}

bootstrap().catch((error: Error) => console.error(error));
