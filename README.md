# skyland cloud - 空岛云

nest new skyland-app && pnpm create vite client --template react-ts

Node.js 开发运维平台, 提供远程调试、业务日志、链路追踪、自定义监控等能力



### 技术选型

作为空岛项目服务端, 我们只关注领域模型, 即数据设计如何合理、安全、扩展、性能高。

界面如何展示更好更方便, 那是前端思考的事, 前端可以提出你的想法和建议, 但最终协议话语权必须在服务端侧。

语言: Node.js / v22
框架: Nest.js / v11
数据库: Postgres / v16
缓存库: Redis & RedisCluster / v7 兼容主从和集群版
其它工具: Docker、Docker Compose、Postman、SwaggerUI

### 产品功能设计




### 领域模型设计

平台 platform

不同容器部署平台接入空岛后, 由于容器团队底层能力不同, 部分功能使用细节上会有差异
计划支持的平台包括: 弹内 Aone、AoneFaaS, 公有云 SAE、FC3.0, 不排除公有云自建 ACK 集群的可能

项目 project



用户 user

团队 team

部署 deployment

事件 event

活动 activity

域名 domain

日志 log

指标 metric

其它

最近 recent

设置 setting

收藏 collection



### 如何参与开发

1. 下载依赖包: `npm install`

2. 启动依赖服务: `docker compose up -d`

3. 启动后端服务: `npm run dev`

4. 通过 SwaggerUI 调试接口: `open https://127.0.0.1:6001/docs`


### 接入亮点功能

1. 支持随意升级 Node.js 版本

2. 引入 Node.js 应用质量评分机制
    0-20 为 D; 30-50 为 C; 60-70 为 B; 80-90 为 A; 100 为 A+
    依赖风险: Node.js 版本大于 v20(+10)
    依赖风险: 生产环境直接依赖包小于 10 个(+10)
    依赖风险: 生产环境都是零依赖包(+10)
    依赖风险: 不存在过时和风险社区包(+10)
    依赖风险: node_modules 目录低于 100Mb(+10)
    风格检测: prettier 未检测到异常(+10)
    风格检测: lint 和 tsc 未检测到异常(+10)
    单元测试: 存在单测且均通过(+10)
    行覆盖率: 单测行覆盖率达到80%(+10)
    代码审查: AI Agent 评测(0-10 不等)

3. 支持随意切换 Koa、Express 等 Web 框架
    接入空岛不强制要求版本, 但建议升级到 Nodejs v20 及以上, 平台部分实用功能在低版本下存在兼容性问题

4. 支持远程断点调试 & 预发流量转到本地调试
    启动时通过 --inspect 或 --debug 参数, 然后通过本地 IDE 或 Chrome DevTools 进行断点调试
    查询 Node.js 相关进程: ps aux | grep node; 激活 Node.js 调试器: kill -USR1 <PID>; 关闭则使用 USR2 信号
    远程调试不稳定分析: Nodejs 版本在 18 和 20 之间, 信号调试器特性有兼容性问题; USR2 关闭过调试器导致无法重新开启
    方案上: 仅支持预发用 & 检测 9229 端口存活 & 如何暴露端口出去并集成 Chrome DevTools & 定时检测和自动关闭调试器 

5. 支持实时 dump 数据分析 CPU、Memory 问题
    

### 一体化设计

选型: Vite + React + Umi + Antd 降开发成本, 服务端 Nest.js; 均支持自定义框架(前端 Umi + Antd 可自行更换)

前后端一体化项目要怎么设计比较好? 这是我们得出的结论:

  1、开发环境: npm run dev 前后端同时启动, 通过 Umi 插件将 /api 的请求转发到 Nest.js 服务端, 避免跨域问题
  2、生产环境: 前端部署到 OSS/CDN, 服务端打包为 tar 包或容器镜像, 进行函数部署

是否需要共享一些依赖? 
  个人觉得没必要, 尽可能保持前后端各自的独立性, 一体化应用本身只是个便捷解决方案, 如果在编码层都耦合了, 以后迁移和架构升级就更难受了 

如何实现前端发 OSS/CDN, 域名解析到服务端函数, 那服务端还需要去加载或配置 CDN 静态资源吗?
  这块逻辑还不太清楚, 而且这个还得是泛解析域名, 回头再研究下。


### 其他问题


### 附录: 镜像内置软件

os8-1.0

切换语言版本: nvm、 pyenv、gvm

当前预装语言版本: Node.js v18、v20、v22, 默认 v20
使用默认版本, 应用构建的速度大概能从 3 分钟降低到 1 分钟, 也能减少一部分网络原因导致构建失败的问题
如果你需要更新的版本或指定小版本号, 只需 package.json 中填上: "config.install-node: 24/18.20.8"

packer、ping、traceroute


### 

