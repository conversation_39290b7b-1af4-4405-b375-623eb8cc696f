networks:
  app-network:
    driver: bridge

services:
  # Redis 单机版
  redis:
    image: redis:7.2-alpine
    container_name: redis
    restart: always
    ports:
      - "16379:6379"
    volumes:
      - redis-data:/data
    networks:
      - app-network

  # PostgreSQL 服务
  postgres:
    image: postgres:16-alpine
    container_name: postgres
    restart: always
    ports:
      - "15432:5432"
    environment:
      POSTGRES_USER: nestjs
      POSTGRES_PASSWORD: nestjs
      POSTGRES_DB: nestjs_dev
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - app-network

  # pgAdmin/CloudQuery - 数据集成工具

  # Node.js 应用服务
#  node-app:
#    build:
#      context: .
#      dockerfile: Dockerfile
#    container_name: node-app
#    depends_on:
#      - redis
#      - postgres
#    ports:
#      - "13000:3000"
#    volumes:
#      - .:/app:delegated
#      - /app/node_modules
#    environment:
#      DB_HOST: postgres
#      DB_PORT: 5432
#      DB_USERNAME: nestjs
#      DB_PASSWORD: nestjs
#      DB_DATABASE: nestjs_dev
#      REDIS_HOST: redis
#      REDIS_PORT: 6379
#    networks:
#      - app-network
#    command: npm run start:dev

volumes:
  redis-data:
  postgres-data:    