import type { RouteObject } from "react-router-dom";

// 扩展 RouteObject 类型，添加我们需要的属性
export interface CustomRouteObject
  extends Omit<RouteObject, "element" | "children"> {
  element: () => Promise<{ default: React.ComponentType<any> }>;
  children?: CustomRouteObject[];
  meta?: {
    title?: string;
    icon?: string;
    order?: number;
    requiresAuth?: boolean;
    roles?: string[];
    hidden?: boolean;
  };
  name?: string;
}

// 路由配置表
export const routes: Array<CustomRouteObject> = [
  {
    path: "/",
    element: () => import("../layouts/AdminLayout"),
    meta: { title: "首页", icon: "home", order: 1, requiresAuth: true },
    children: [
      {
        index: true,
        name: "dashboard",
        element: () => import("../pages/dashboard"),
        meta: {
          title: "仪表盘",
          icon: "dashboard",
          order: 2,
          requiresAuth: true,
        },
      },
      {
        path: "users",
        name: "users",
        element: () => import("../pages/users"),
        meta: {
          title: "用户管理",
          icon: "users",
          order: 3,
          requiresAuth: true,
          roles: ["admin"],
        },
      },
      {
        path: "settings",
        name: "settings",
        meta: {
          title: "系统设置",
          icon: "settings",
          order: 4,
          requiresAuth: true,
        },
        children: [
          {
            index: true,
            name: "settings-general",
            element: () => import("../pages/settings/general"),
            meta: { title: "常规设置", requiresAuth: true },
          },
          {
            path: "profile",
            name: "settings-profile",
            element: () => import("../pages/settings/profile"),
            meta: { title: "个人资料", requiresAuth: true },
          },
        ],
      },
    ],
  },
  {
    path: "/auth",
    // element: () => import("../layouts/PublicLayout"),
    children: [
      {
        path: "login",
        name: "login",
        element: () => import("../pages/auth/login"),
        meta: { title: "登录" },
      },
      {
        path: "register",
        name: "register",
        element: () => import("../pages/auth/register"),
        meta: { title: "注册" },
      },
    ],
  },
  {
    path: "*",
    name: "not-found",
    element: () => import("../pages/errors/404"),
    meta: { title: "页面不存在" },
  },
  {
    path: "/500",
    name: "server-error",
    element: () => import("../pages/errors/500"),
    meta: { title: "服务器错误" },
  },
];
