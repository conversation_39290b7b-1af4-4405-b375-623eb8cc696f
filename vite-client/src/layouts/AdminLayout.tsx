import React from 'react';
import { Outlet, useNavigate } from 'react-router-dom';
import { Layout, Menu, Breadcrumb, theme } from 'antd';
import {
    UserOutlined,
    DashboardOutlined,
    SettingOutlined,
    LogoutOutlined,
} from '@ant-design/icons';

const { Header, Sider, Content, Footer } = Layout;

interface AdminLayoutProps {
    /* 默认是否展开 */
    collapsed?: boolean;
    onCollapse?: (collapsed: boolean) => void;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ collapsed = true, onCollapse = () => {} }) => {
    const { token: { colorBgContainer } } = theme.useToken();
    const navigate = useNavigate();

    const logout = () => {
        localStorage.removeItem('token');
        navigate('/');
    };

    return (
        <Layout style={{ minHeight: '100vh' }}>
            <Sider collapsible collapsed={collapsed} onCollapse={onCollapse}>
                <div className="logo" style={{ display: 'flex', alignItems: 'center' }}>
                    {!collapsed && <h1 style={{ color: 'white', margin: 0 }}>管理系统</h1>}
                </div>
                <Menu
                    theme="dark"
                    mode="inline"
                    defaultSelectedKeys={['1']}
                    items={[
                        {
                            key: '1',
                            icon: <DashboardOutlined />,
                            label: '仪表盘',
                            onClick: () => navigate('/admin/dashboard'),
                        },
                        {
                            key: '2',
                            icon: <UserOutlined />,
                            label: '用户管理',
                            onClick: () => navigate('/admin/users'),
                        },
                        {
                            key: '3',
                            icon: <SettingOutlined />,
                            label: '系统设置',
                            onClick: () => navigate('/admin/settings'),
                        },
                    ]}
                />
            </Sider>
            <Layout className="site-layout">
                <Header
                    style={{
                        padding: 0,
                        background: colorBgContainer,
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                    }}
                >
                    <div style={{ padding: '0 24px' }}>
                        <button
                            type="button"
                            style={{ background: 'transparent', border: 'none', cursor: 'pointer' }}
                            onClick={() => onCollapse(!collapsed)}
                        >
                            {collapsed ? <span className="anticon anticon-menu-unfold" /> : <span className="anticon anticon-menu-fold" />}
                        </button>
                    </div>
                    <div style={{ padding: '0 24px' }}>
                        <Menu mode="horizontal" items={[
                            {
                                key: 'logout',
                                icon: <LogoutOutlined />,
                                label: '退出登录',
                                onClick: logout,
                            }
                        ]} />
                    </div>
                </Header>
                <Content
                    style={{
                        margin: '16px 24px',
                        padding: 24,
                        minHeight: 280,
                        background: colorBgContainer,
                    }}
                >
                    <Breadcrumb style={{ margin: '16px 0' }}>
                        <Breadcrumb.Item>首页</Breadcrumb.Item>
                        <Breadcrumb.Item>管理后台</Breadcrumb.Item>
                    </Breadcrumb>
                    <Outlet />
                </Content>
                <Footer style={{ textAlign: 'center' }}>Ant Design ©2023 Created by Ant UED</Footer>
            </Layout>
        </Layout>
    );
};

export default AdminLayout;