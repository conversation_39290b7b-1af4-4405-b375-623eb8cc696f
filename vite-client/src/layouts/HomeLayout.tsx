import React from 'react';
import { Layout, Carousel, Card, Row, Col, Button, Menu } from 'antd';
import { UserOutlined, ShoppingCartOutlined, CreditCardOutlined } from '@ant-design/icons';

const { Header, Content, Footer } = Layout;

const HomePage: React.FC = () => {
    const contentStyle = {
        height: '160px',
        color: '#fff',
        lineHeight: '160px',
        textAlign: 'center' as const,
        background: '#364d79',
    };

    return (
        <Layout>
            <Header style={{ position: 'fixed', zIndex: 1, width: '100%' }}>
                <div className="logo" style={{ float: 'left', width: '120px', height: '31px', margin: '16px 24px 16px 0', background: 'rgba(255, 255, 255, 0.3)' }} />
                <Menu
                    theme="dark"
                    mode="horizontal"
                    defaultSelectedKeys={['1']}
                    items={[
                        { key: '1', label: '首页' },
                        { key: '2', label: '产品' },
                        { key: '3', label: '服务' },
                        { key: '4', label: '关于我们' },
                    ]}
                />
            </Header>
            <Content className="site-layout" style={{ padding: '0 50px', marginTop: 64 }}>
                <div style={{ background: '#fff', padding: 24, minHeight: 380 }}>
                    {/* 轮播图 */}
                    <Carousel autoplay>
                        <div>
                            <h3 style={contentStyle}>轮播图1</h3>
                        </div>
                        <div>
                            <h3 style={contentStyle}>轮播图2</h3>
                        </div>
                        <div>
                            <h3 style={contentStyle}>轮播图3</h3>
                        </div>
                        <div>
                            <h3 style={contentStyle}>轮播图4</h3>
                        </div>
                    </Carousel>

                    {/* 功能卡片 */}
                    <Row gutter={16} style={{ marginTop: 24 }}>
                        <Col span={8}>
                            <Card title="用户管理" bordered={false} hoverable>
                                <p><UserOutlined style={{ fontSize: '24px', color: '#1890ff' }} /></p>
                                <p>管理系统用户，包括添加、编辑和删除</p>
                                <Button type="primary" style={{ marginTop: 16 }}>进入</Button>
                            </Card>
                        </Col>
                        <Col span={8}>
                            <Card title="订单管理" bordered={false} hoverable>
                                <p><ShoppingCartOutlined style={{ fontSize: '24px', color: '#1890ff' }} /></p>
                                <p>查看和处理用户订单，跟踪物流状态</p>
                                <Button type="primary" style={{ marginTop: 16 }}>进入</Button>
                            </Card>
                        </Col>
                        <Col span={8}>
                            <Card title="财务管理" bordered={false} hoverable>
                                <p><CreditCardOutlined style={{ fontSize: '24px', color: '#1890ff' }} /></p>
                                <p>查看收入支出报表，管理财务数据</p>
                                <Button type="primary" style={{ marginTop: 16 }}>进入</Button>
                            </Card>
                        </Col>
                    </Row>
                </div>
            </Content>
            <Footer style={{ textAlign: 'center' }}>©2023 版权所有</Footer>
        </Layout>
    );
};

export default HomePage;