import { message as Message } from "antd";
import axios, {
  type AxiosError,
  type AxiosRequestConfig,
  type AxiosResponse,
  type InternalAxiosRequestConfig,
} from "axios";

import type { Result } from "../../types/api.ts";
import { ResultEnum } from "../../types/enum.ts";

// import { t } from '@/locales/i18n';
const t = (en: string) => en;

const httpClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 5000,
  headers: { "Content-Type": "application/json;charset=utf-8" },
});

// 请求拦截
httpClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 在发送请求之前做些什么, 比如全局设置请求头 token
    return config;
  },
  (error: AxiosError) => {
    // 对请求错误做些什么
    return Promise.reject(error);
  },
);

// 响应拦截
httpClient.interceptors.response.use(
  (res: AxiosResponse<Result>) => {
    if (!res.data) throw new Error(t("sys.api.apiRequestFailed"));

    const { status, data, message } = res.data;
    // 业务请求成功
    const hasSuccess =
      data && Reflect.has(res.data, "status") && status === ResultEnum.SUCCESS;
    if (hasSuccess) {
      return data;
    }

    // 业务请求错误
    throw new Error(message || t("sys.api.apiRequestFailed"));
  },
  (error: AxiosError<Result>) => {
    const { response, message } = error || {};
    let errMsg = "";
    try {
      errMsg = response?.data?.message || message;
    } catch (error) {
      // 抛出未知的错误堆栈
      throw new Error(error as unknown as string);
    }
    // 对响应错误做点什么
    if (!errMsg) {
      // TODO 根据不同错误码, 弹窗要提示的错误信息不一样, 401 可能还需要跳转登录页
      // 最终兜底的业务报错提示信息
      errMsg = t("sys.api.errorMessage");
    }
    Message.error(errMsg);
    return Promise.reject(error);
  },
);

class APIClient {
  get<T = never>(config: AxiosRequestConfig): Promise<T> {
    return this.request({ ...config, method: "GET" });
  }

  post<T = never>(config: AxiosRequestConfig): Promise<T> {
    return this.request({ ...config, method: "POST" });
  }

  put<T = never>(config: AxiosRequestConfig): Promise<T> {
    return this.request({ ...config, method: "PUT" });
  }

  delete<T = never>(config: AxiosRequestConfig): Promise<T> {
    return this.request({ ...config, method: "DELETE" });
  }

  request<T = never>(config: AxiosRequestConfig): Promise<T> {
    return new Promise((resolve, reject) => {
      httpClient
        .request<never, AxiosResponse<Result>>(config)
        .then((res: AxiosResponse<Result>) => {
          resolve(res as unknown as Promise<T>);
        })
        .catch((e: Error | AxiosError) => {
          reject(e);
        });
    });
  }
}
export default new APIClient();
