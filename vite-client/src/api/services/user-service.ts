import httpClient from "../http-client.ts";
import type { UserInfo, UserToken } from "../../../types/entity.ts";

export interface SignInReq {
  account: string;
  password: string;
}

export interface SignUpReq extends SignInReq {
  email: string;
}

export type SignInRes = UserToken & { user: UserInfo };

export enum UserApi {
  SignIn = "/auth/signin",
  SignUp = "/auth/signup",
  Logout = "/auth/logout",
  Refresh = "/auth/refresh",
  User = "/user",
}

const signin = (data: SignInReq) =>
  httpClient.post<SignInRes>({ url: UserApi.SignIn, data });
const signup = (data: SignUpReq) =>
  httpClient.post<SignInRes>({ url: UserApi.SignUp, data });
const logout = () => httpClient.get({ url: UserApi.Logout });
const findById = (id: string) =>
  httpClient.get<UserInfo[]>({ url: `${UserApi.User}/${id}` });

export default {
  signin,
  signup,
  findById,
  logout,
};
