.global-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 20px;
}

.global-loading.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 9999;
}

.global-loading.container {
    width: 100%;
    height: 100%;
    min-height: 200px;
}

.loading-text {
    margin-top: 12px;
    color: rgba(0, 0, 0, 0.65);
}

/* 添加淡入淡出动画 */
.global-loading {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}