import React from 'react';
import { Spin } from 'antd';
import type { SpinProps } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';

import './styles.css';

export interface LoadingProps extends Omit<SpinProps, 'indicator'> {
    /** 自定义加载图标 */
    icon?: React.ReactNode;
    /** 加载文本 */
    text?: string;
    /** 加载样式类型 */
    type?: 'default' | 'fullscreen' | 'container';
    /** 自定义样式 */
    className?: string;
}

const Loading: React.FC<LoadingProps> = ({
     icon,
     text = 'Loading...',
     type = 'default',
     className = '',
     ...spinProps
 }) => {
    // 默认加载图标
    const defaultIcon = <LoadingOutlined style={{ fontSize: 32 }} spin />;
    const indicator = icon || defaultIcon;

    const containerClass = `global-loading ${type} ${className}`;

    return (
        <div className={containerClass}>
            <Spin indicator={indicator} {...spinProps}>
                {text && <div className="loading-text">{text}</div>}
            </Spin>
        </div>
    );
};

export default Loading;