# React + TypeScript + Vite + Ant design

Umi 存在一些过时依赖、依赖层级较深等技术隐患, 空岛一体化项目优先选用 Vite + Antd 开发模式, 对大多数同学来说上手成本没啥差。

而对于另一部分对技术更有追求、更希望跟进社区方向的同学, 我们也提供了 Vite + Next.js + Tailwind 的中后台集成方案。 

如果你都不喜欢, 或者想用以前的 Umi + Antd, 我们也支持自定义前端项目框架, 你可以 Copy 之前的 Umi 项目过来用。

另外, 除集团内部的 tnpm 之外, 我们也支持了使用 pnpm 管理依赖, 并且建议你优先使用 pnpm 进行依赖管理, 减少风险。

### 目录规范约定

内部中后台项目, 大都实现的很不优雅, 命名都乱七八糟, 更别说代码了, 读着都头大。
这里面向前端同学, 提供几段规范建议, 让大家达成共识减少点阅读成本, 也防止你的代码被人当成饭后槽点:

1、页面都是现在 pages 目录下, 以 小写 + 中划线（kebab-case）命名
2、页面是直接交给 Route 渲染的, 需要有完整的页面结构, 可以使用 Hooks 和 Components
3、某页面专用组件放 pages/{demo-page} 下 components 目录, 项目级共享组件放 src/components 下
4、每个组件都实现在一个文件夹中, 入口统一为 index.tsx, 文件夹和组件名称应该是 大写开头（PascalCase）命名
5、组件和 Route 不应有任何关联, 仅通过 props 接收参数, 参数的设计做好默认值控制, 组件尽可能少暴露配置字段
6、需要多种布局方式的项目, 可以在 layouts 下通过 PascalCase 命名方式实现页面布局组合, 可以参考本模板项目
7、自定义 Hooks 使用 小写开头（camelCase）的函数式命名习惯, 如 useAuth、useTable、useClipboard 等等
8、其他如数据模型文件、types 类型定义、i18n 多语言、, 建议均使用小写 + 中划线（kebab-case）方式命名


### 路由和布局设计

App.tsx 作为根组件, 通常处理一些路由、全局状态、顶级布局, 但它下一步是去加载 Layout 组件还是 Page 组件加载 Layout 呢? 

都不是。而是通过 React Router 将 Path 和 Page 绑定, Page 可以自行选择使用哪种 Layout 方式。