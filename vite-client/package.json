{"name": "skyland-client", "private": true, "version": "0.0.1", "type": "module", "scripts": {"install-deps": "pnpm install", "dev": "vite --port 8000", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "antd": "^5.26.4", "axios": "^1.10.0", "normalize.css": "^8.0.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "prettier": "^3.6.2", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0", "vite-tsconfig-paths": "^5.1.4"}}